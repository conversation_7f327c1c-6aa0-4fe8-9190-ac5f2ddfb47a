/**
 * 检查TUS上传管理器初始化
 */

console.log('🔍 检查TUS上传管理器初始化...');

// 模拟主进程中的配置
const uploadConfig = {
  endpoint: "http://localhost:1080/files",
  chunkSize: 5 * 1024 * 1024,
  retryDelays: [0, 1000, 3000, 5000],
  parallelUploads: 10,
  enableQueue: true, // 关键配置
  queueConfig: {
    maxConcurrent: 10,
    maxRetries: 3,
    retryDelay: 1000,
    progressUpdateInterval: 500,
    storageUpdateInterval: 2000,
    enablePriority: true,
  },
};

console.log('⚙️ 配置检查:');
console.log('  enableQueue:', uploadConfig.enableQueue);
console.log('  parallelUploads:', uploadConfig.parallelUploads);
console.log('  maxConcurrent:', uploadConfig.queueConfig.maxConcurrent);

// 检查默认配置
console.log('\n📋 检查默认配置函数...');

// 模拟createDefaultTusConfig的逻辑
function createDefaultTusConfig(endpoint) {
  return {
    endpoint: endpoint || "http://localhost:1080/files",
    chunkSize: 5 * 1024 * 1024, // 5MB
    retryDelays: [0, 1000, 3000, 5000],
    parallelUploads: 10, // 提升默认并发数
    enableQueue: true, // 默认启用队列系统
    queueConfig: {
      maxConcurrent: 10,
      maxRetries: 3,
      retryDelay: 1000,
      progressUpdateInterval: 500,
      storageUpdateInterval: 2000,
      enablePriority: true,
    },
    headers: {
      // 认证头由uploadManager在onBeforeRequest中动态添加
      // 这里可以添加其他静态头信息
    },
  };
}

const defaultConfig = createDefaultTusConfig();
console.log('默认配置:');
console.log('  enableQueue:', defaultConfig.enableQueue);
console.log('  parallelUploads:', defaultConfig.parallelUploads);

// 模拟配置合并
const finalConfig = {
  ...defaultConfig,
  ...uploadConfig,
};

console.log('\n🔧 最终配置:');
console.log('  enableQueue:', finalConfig.enableQueue);
console.log('  parallelUploads:', finalConfig.parallelUploads);
console.log('  maxConcurrent:', finalConfig.queueConfig.maxConcurrent);

// 检查配置是否正确
if (finalConfig.enableQueue === true) {
  console.log('✅ 队列系统应该启用');
} else {
  console.log('❌ 队列系统未启用');
}

if (finalConfig.queueConfig && finalConfig.queueConfig.maxConcurrent > 0) {
  console.log('✅ 最大并发数配置正确');
} else {
  console.log('❌ 最大并发数配置错误');
}

console.log('\n🎯 结论:');
console.log('如果队列系统仍然不工作，问题可能在于:');
console.log('1. TusUploadManager构造函数中的队列初始化逻辑');
console.log('2. 队列事件监听器设置');
console.log('3. 队列调度器的启动');
console.log('4. 任务执行器的集成');

console.log('\n💡 建议:');
console.log('1. 在TusUploadManager构造函数中添加更多日志');
console.log('2. 检查queueEnabled标志是否正确设置');
console.log('3. 验证队列组件是否正确初始化');
console.log('4. 确认事件监听器是否正确设置');
