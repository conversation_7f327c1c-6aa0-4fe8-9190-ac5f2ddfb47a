/**
 * 队列系统调试工具
 * 用于验证队列系统是否正确工作
 */

import { TusUploadManager, createDefaultTusConfig } from "../index";
import { TaskPriority } from "../queue";

/**
 * 模拟创建上传任务
 */
function createMockUploadTask(uploadManager: TusUploadManager, taskId: string): void {
  // 直接在内存中创建一个模拟任务
  const mockTask = {
    id: taskId,
    fileName: `mock_file_${taskId}.txt`,
    filePath: `/tmp/mock_${taskId}.txt`,
    fileSize: 1024 * 10, // 10KB
    status: "pending" as const,
    progress: 0,
    bytesUploaded: 0,
    createdAt: new Date(),
    metadata: {},
  };

  // 直接添加到任务列表（绕过文件系统检查）
  (uploadManager as any).tasks.set(taskId, mockTask);
  console.log(`✅ 创建模拟任务: ${taskId}`);
}

/**
 * 测试队列系统
 */
async function testQueueSystem(): Promise<void> {
  console.log("🧪 开始队列系统调试测试...");
  
  // 创建配置
  const config = {
    ...createDefaultTusConfig(),
    enableQueue: true,
    parallelUploads: 3,
    queueConfig: {
      maxConcurrent: 3,
      maxRetries: 3,
      retryDelay: 1000,
      progressUpdateInterval: 500,
      storageUpdateInterval: 1000,
      enablePriority: true,
    },
  };

  console.log("⚙️ 配置:", config);

  // 创建上传管理器
  const uploadManager = new TusUploadManager(config);

  // 检查队列系统状态
  console.log("\n📊 初始队列系统状态:");
  const systemStatus = uploadManager.getQueueSystemStatus();
  console.log(systemStatus);

  // 创建多个模拟任务
  const taskIds = ["task1", "task2", "task3", "task4", "task5"];
  
  console.log("\n📋 创建模拟任务...");
  for (const taskId of taskIds) {
    createMockUploadTask(uploadManager, taskId);
  }

  // 启动所有任务
  console.log("\n🚀 启动所有任务...");
  for (let i = 0; i < taskIds.length; i++) {
    const taskId = taskIds[i];
    const priority = i === 0 ? TaskPriority.HIGH : TaskPriority.NORMAL;
    
    console.log(`\n--- 启动任务 ${i + 1}: ${taskId} (优先级: ${priority}) ---`);
    await uploadManager.startUpload(taskId, priority);
    
    // 检查队列状态
    const stats = uploadManager.getQueueStats();
    console.log(`队列状态: 待处理=${stats?.pendingTasks}, 运行中=${stats?.runningTasks}, 已完成=${stats?.completedTasks}`);
  }

  // 监控队列状态变化
  console.log("\n⏳ 监控队列状态变化...");
  for (let i = 0; i < 10; i++) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const stats = uploadManager.getQueueStats();
    const systemStatus = uploadManager.getQueueSystemStatus();
    
    console.log(`\n📊 第${i + 1}秒状态:`);
    console.log(`  队列: 待处理=${stats?.pendingTasks}, 运行中=${stats?.runningTasks}, 已完成=${stats?.completedTasks}`);
    console.log(`  系统: 队列启用=${systemStatus.queueEnabled}, 待存储=${systemStatus.pendingStorageUpdates}`);
    
    if (stats && stats.pendingTasks === 0 && stats.runningTasks === 0) {
      console.log("✅ 所有任务处理完成");
      break;
    }
  }

  // 测试队列管理功能
  console.log("\n🔧 测试队列管理功能...");
  
  // 添加更多任务
  const extraTaskIds = ["extra1", "extra2", "extra3"];
  for (const taskId of extraTaskIds) {
    createMockUploadTask(uploadManager, taskId);
    await uploadManager.startUpload(taskId);
  }
  
  console.log("暂停队列...");
  uploadManager.pauseQueue();
  
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log("恢复队列...");
  uploadManager.resumeQueue();
  
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log("设置最大并发数为5...");
  uploadManager.setMaxConcurrent(5);
  
  const finalStats = uploadManager.getQueueStats();
  console.log("最终队列状态:", finalStats);
  
  console.log("\n✅ 调试测试完成!");
}

/**
 * 运行调试测试
 */
async function runDebugTest(): Promise<void> {
  try {
    await testQueueSystem();
  } catch (error) {
    console.error("\n❌ 调试测试失败:", error);
  }
}

// 如果直接运行此文件，执行调试测试
if (require.main === module) {
  runDebugTest();
}

export { runDebugTest };
