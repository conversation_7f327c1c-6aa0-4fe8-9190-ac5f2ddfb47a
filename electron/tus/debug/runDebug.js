/**
 * 运行队列调试测试
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 运行TUS队列系统调试测试...');

// 使用ts-node运行TypeScript文件
const debugFile = path.join(__dirname, 'queueDebug.ts');
const projectRoot = path.join(__dirname, '../../..');

const child = spawn('npx', ['ts-node', debugFile], {
  cwd: projectRoot,
  stdio: 'inherit',
  shell: true
});

child.on('close', (code) => {
  if (code === 0) {
    console.log('\n✅ 调试测试完成');
  } else {
    console.log(`\n❌ 调试测试失败，退出码: ${code}`);
  }
});

child.on('error', (error) => {
  console.error('\n💥 运行调试测试时出错:', error.message);
});
