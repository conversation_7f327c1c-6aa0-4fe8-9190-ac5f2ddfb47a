/**
 * 简单的队列系统测试
 * 使用编译后的JavaScript文件
 */

console.log('🧪 开始简单的队列系统测试...');

// 模拟测试队列逻辑
function testQueueLogic() {
  console.log('\n📋 测试队列基本逻辑...');
  
  // 模拟队列状态
  let queue = [];
  let running = new Map();
  let maxConcurrent = 3;
  let isProcessing = true;
  let paused = false;
  
  // 模拟任务
  const tasks = ['task1', 'task2', 'task3', 'task4', 'task5'];
  
  // 添加任务到队列
  console.log('\n🚀 添加任务到队列...');
  tasks.forEach((taskId, index) => {
    const priority = index === 0 ? 10 : 5; // 第一个任务高优先级
    queue.push({ taskId, priority, addedAt: new Date() });
    console.log(`✅ 任务已加入队列: ${taskId} (优先级: ${priority})`);
  });
  
  // 按优先级排序
  queue.sort((a, b) => b.priority - a.priority);
  console.log('\n📊 队列排序后:', queue.map(t => `${t.taskId}(${t.priority})`).join(', '));
  
  // 模拟队列处理
  console.log('\n🔄 开始处理队列...');
  
  function canStartNewTask() {
    return isProcessing && !paused && running.size < maxConcurrent && queue.length > 0;
  }
  
  function processQueue() {
    console.log(`\n📊 队列状态: 待处理=${queue.length}, 运行中=${running.size}/${maxConcurrent}, 暂停=${paused}`);
    
    while (canStartNewTask()) {
      const task = queue.shift();
      if (!task) break;
      
      console.log(`🚀 开始执行任务: ${task.taskId}`);
      
      // 模拟任务执行
      const taskPromise = new Promise((resolve) => {
        setTimeout(() => {
          console.log(`✅ 任务完成: ${task.taskId}`);
          running.delete(task.taskId);
          resolve();
          
          // 任务完成后继续处理队列
          if (queue.length > 0) {
            setTimeout(processQueue, 100);
          }
        }, Math.random() * 2000 + 1000); // 1-3秒随机执行时间
      });
      
      running.set(task.taskId, taskPromise);
    }
    
    if (!canStartNewTask() && queue.length > 0) {
      console.log(`⏸️ 无法启动新任务: 处理中=${isProcessing}, 暂停=${paused}, 运行中=${running.size}/${maxConcurrent}, 队列=${queue.length}`);
    }
  }
  
  // 开始处理
  processQueue();
  
  // 测试暂停/恢复
  setTimeout(() => {
    console.log('\n⏸️ 暂停队列...');
    paused = true;
  }, 3000);
  
  setTimeout(() => {
    console.log('\n▶️ 恢复队列...');
    paused = false;
    processQueue();
  }, 5000);
  
  // 测试设置并发数
  setTimeout(() => {
    console.log('\n⚙️ 设置最大并发数为5...');
    maxConcurrent = 5;
    processQueue();
  }, 7000);
  
  // 检查最终状态
  setTimeout(() => {
    console.log(`\n📊 最终状态: 待处理=${queue.length}, 运行中=${running.size}`);
    if (queue.length === 0 && running.size === 0) {
      console.log('✅ 所有任务处理完成!');
    }
  }, 10000);
}

// 运行测试
testQueueLogic();

console.log('\n⏳ 测试运行中，请等待10秒...');
