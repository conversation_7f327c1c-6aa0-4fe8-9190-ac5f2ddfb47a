/**
 * 测试队列系统修复
 */

console.log('🧪 测试队列系统修复...');

// 测试配置逻辑
function testConfigLogic() {
  console.log('\n🔧 测试配置逻辑...');
  
  // 测试不同的配置情况
  const testCases = [
    { enableQueue: true, expected: true, description: '明确启用' },
    { enableQueue: false, expected: false, description: '明确禁用' },
    { enableQueue: undefined, expected: false, description: '未定义' },
    { enableQueue: null, expected: false, description: 'null值' },
    {}, // 没有enableQueue属性
  ];
  
  testCases.forEach((testCase, index) => {
    const config = testCase;
    
    // 原来的逻辑（错误的）
    const oldLogic = config.enableQueue !== false;
    
    // 新的逻辑（正确的）
    const newLogic = config.enableQueue === true;
    
    console.log(`\n测试用例 ${index + 1}: ${testCase.description || '空配置'}`);
    console.log(`  配置: enableQueue = ${config.enableQueue}`);
    console.log(`  旧逻辑结果: ${oldLogic}`);
    console.log(`  新逻辑结果: ${newLogic}`);
    console.log(`  期望结果: ${testCase.expected || false}`);
    console.log(`  新逻辑正确: ${newLogic === (testCase.expected || false) ? '✅' : '❌'}`);
  });
}

// 测试队列初始化逻辑
function testQueueInitialization() {
  console.log('\n🚀 测试队列初始化逻辑...');
  
  // 模拟TusUploadManager构造函数的逻辑
  function simulateConstructor(config) {
    console.log(`\n--- 模拟构造函数调用 ---`);
    console.log(`输入配置:`, config);
    
    const queueEnabled = config.enableQueue === true;
    console.log(`🔧 queueEnabled = ${queueEnabled}`);
    
    if (queueEnabled) {
      console.log('✅ 队列系统应该初始化');
      
      // 模拟队列配置
      const queueConfig = {
        maxConcurrent: config.parallelUploads || 10,
        ...config.queueConfig,
      };
      console.log('📋 队列配置:', queueConfig);
      
      console.log('🎬 启动队列处理...');
      console.log('✅ 队列系统初始化完成');
    } else {
      console.log('❌ 队列系统未启用');
    }
    
    return { queueEnabled };
  }
  
  // 测试不同配置
  const configs = [
    {
      enableQueue: true,
      parallelUploads: 10,
      queueConfig: { maxConcurrent: 10 }
    },
    {
      enableQueue: false,
      parallelUploads: 10
    },
    {
      parallelUploads: 10
      // 没有enableQueue
    }
  ];
  
  configs.forEach((config, index) => {
    console.log(`\n🧪 测试配置 ${index + 1}:`);
    const result = simulateConstructor(config);
    console.log(`结果: queueEnabled = ${result.queueEnabled}`);
  });
}

// 运行测试
testConfigLogic();
testQueueInitialization();

console.log('\n🎯 总结:');
console.log('1. 修复了配置逻辑，现在只有明确设置enableQueue=true时才启用队列');
console.log('2. 添加了详细的日志输出，便于调试');
console.log('3. 队列系统应该能正确初始化了');

console.log('\n📝 下一步:');
console.log('1. 重新启动应用程序');
console.log('2. 查看控制台日志，确认队列系统是否正确初始化');
console.log('3. 测试上传大量小文件，观察是否按队列方式执行');
