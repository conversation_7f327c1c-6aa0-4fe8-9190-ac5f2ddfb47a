/**
 * 简单的队列系统测试
 * 验证队列管理系统是否正常工作
 */

import { TusUploadManager, createDefaultTusConfig } from "../index";
import { TaskPriority } from "../queue";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";

// 测试配置
const TEST_CONFIG = {
  endpoint: "http://localhost:1080/files",
  enableQueue: true,
  parallelUploads: 3, // 设置为3便于观察
  queueConfig: {
    maxConcurrent: 3,
    progressUpdateInterval: 500,
    storageUpdateInterval: 1000,
  },
};

/**
 * 创建测试文件
 */
async function createTestFile(name: string, sizeKB: number): Promise<string> {
  const tempDir = os.tmpdir();
  const filePath = path.join(tempDir, `test_${name}_${Date.now()}.txt`);
  
  // 创建指定大小的测试文件
  const content = "x".repeat(sizeKB * 1024);
  await fs.promises.writeFile(filePath, content);
  
  return filePath;
}

/**
 * 清理测试文件
 */
async function cleanupTestFile(filePath: string): Promise<void> {
  try {
    await fs.promises.unlink(filePath);
  } catch (error) {
    console.warn(`清理测试文件失败: ${filePath}`, error);
  }
}

/**
 * 测试队列基本功能
 */
async function testQueueBasics(): Promise<void> {
  console.log("\n=== 测试队列基本功能 ===");
  
  const uploadManager = new TusUploadManager(TEST_CONFIG);
  
  // 创建测试文件
  const testFiles = await Promise.all([
    createTestFile("small1", 10), // 10KB
    createTestFile("small2", 20), // 20KB
    createTestFile("small3", 30), // 30KB
    createTestFile("small4", 40), // 40KB
    createTestFile("small5", 50), // 50KB
  ]);
  
  try {
    console.log("📁 创建了5个测试文件");
    
    // 创建上传任务
    const taskIds: string[] = [];
    for (const filePath of testFiles) {
      const taskId = await uploadManager.createUploadTask(filePath);
      taskIds.push(taskId);
      console.log(`✅ 创建任务: ${taskId} (${path.basename(filePath)})`);
    }
    
    // 检查初始队列状态
    console.log("\n📊 初始状态:");
    const initialStats = uploadManager.getQueueStats();
    console.log("队列统计:", initialStats);
    
    // 启动所有上传任务
    console.log("\n🚀 启动所有上传任务...");
    for (let i = 0; i < taskIds.length; i++) {
      const taskId = taskIds[i];
      const priority = i === 0 ? TaskPriority.HIGH : TaskPriority.NORMAL;
      console.log(`📋 启动任务 ${i + 1}: ${taskId} (优先级: ${priority})`);
      await uploadManager.startUpload(taskId, priority);
    }
    
    // 等待一段时间观察队列处理
    console.log("\n⏳ 等待队列处理...");
    for (let i = 0; i < 10; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const stats = uploadManager.getQueueStats();
      const systemStatus = uploadManager.getQueueSystemStatus();
      
      console.log(`\n📊 第${i + 1}秒状态:`);
      console.log(`  队列: 待处理=${stats?.pendingTasks}, 运行中=${stats?.runningTasks}, 已完成=${stats?.completedTasks}`);
      console.log(`  系统: 待存储=${systemStatus.pendingStorageUpdates}, 待进度=${systemStatus.pendingProgressUpdates}`);
      
      if (stats && stats.pendingTasks === 0 && stats.runningTasks === 0) {
        console.log("✅ 所有任务处理完成");
        break;
      }
    }
    
    // 检查最终状态
    const finalStats = uploadManager.getQueueStats();
    console.log("\n📊 最终状态:", finalStats);
    
  } finally {
    // 清理测试文件
    for (const filePath of testFiles) {
      await cleanupTestFile(filePath);
    }
  }
}

/**
 * 运行测试
 */
async function runTest(): Promise<void> {
  console.log("🧪 开始队列系统测试...");
  
  try {
    await testQueueBasics();
    console.log("\n✅ 测试完成!");
  } catch (error) {
    console.error("\n❌ 测试失败:", error);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTest();
}

export { runTest };
