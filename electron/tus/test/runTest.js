/**
 * 简单的测试运行脚本
 * 用于快速验证队列系统
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🧪 运行TUS队列系统测试...');

try {
  // 编译TypeScript并运行测试
  const testFile = path.join(__dirname, 'queueTest.ts');
  
  // 使用ts-node运行测试
  execSync(`npx ts-node "${testFile}"`, {
    stdio: 'inherit',
    cwd: path.join(__dirname, '../../..')
  });
  
  console.log('\n✅ 测试运行完成');
} catch (error) {
  console.error('\n❌ 测试运行失败:', error.message);
  process.exit(1);
}
